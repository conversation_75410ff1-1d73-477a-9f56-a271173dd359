import pandas as pd
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut, GeocoderServiceError
import time
import ssl
import certifi
import logging
from datetime import datetime
import sys
import os
from tqdm import tqdm

# Bounding box dla powiatu bocheńskiego
# Format dla geopy: [(lat1, lon1), (lat2, lon2)] - południowy-zachód, północny-wschód
# <PERSON><PERSON><PERSON> obejmuj<PERSON>cy powiat bocheński w województwie małopolskim
BOCHNIA_BBOX = [(49.7, 20.2), (50.1, 20.7)]

# Ko<PERSON> pocztowe powiatu bocheńskiego
BOCHNIA_POSTAL_CODES = {
    '32-700', '32-708', '32-709', '32-711', '32-712', '32-720', '32-722', 
    '32-724', '32-725', '32-731', '32-732', '32-733', '32-740', '32-741', 
    '32-742', '32-744', '32-765', '32-813', '32-015'
}

# <PERSON>ache dla miast - przyspiesza wyszukiwanie powtarzających się miast
CITY_CACHE = {}

def extract_postal_code(address):
    """
    Wyodrębnia kod pocztowy z adresu.
    Oczekuje formatu: "XX-XXX Miasto,ulica numer" lub podobnego.
    """
    import re
    if not isinstance(address, str):
        return None
    
    # Szukaj wzorca XX-XXX na początku adresu
    match = re.match(r'^(\d{2}-\d{3})', address.strip())
    if match:
        return match.group(1)
    return None

def is_valid_postal_code(postal_code):
    """
    Sprawdza, czy kod pocztowy należy do powiatu bocheńskiego.
    """
    return postal_code in BOCHNIA_POSTAL_CODES if postal_code else False

def parse_csv_address(address):
    """
    Parsuje adres z pliku CSV w formacie: "XX-XXX Miasto,ulica numer"
    Zwraca słownik z komponentami adresu.
    """
    import re
    if not isinstance(address, str):
        return None
    
    # Wzorzec dla adresu: kod_pocztowy miasto,ulica_i_numer
    pattern = r'^(\d{2}-\d{3})\s+([^,]+),(.+)$'
    match = re.match(pattern, address.strip())
    
    if match:
        postal_code = match.group(1)
        city = match.group(2).strip()
        street_and_number = match.group(3).strip()
        
        return {
            'postal_code': postal_code,
            'city': city,
            'street_and_number': street_and_number,
            'full_address': address
        }
    
    return None

def setup_logging():
    """
    Konfiguruje system logowania.
    """
    # Tworzymy nazwę pliku logu z datą i czasem
    log_filename = f"geocoding_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    # Konfiguracja logowania
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return log_filename

def process_csv(input_file):
    """
    Przetwarza plik CSV z adresami i zapisuje wyniki geokodowania.
    """
    try:
        logging.info(f"Rozpoczynam przetwarzanie pliku: {input_file}")

        # Wczytaj dane - używamy przecinka jako separatora i obsługujemy cudzysłowy
        df = pd.read_csv(input_file, sep=',', quotechar='"', skipinitialspace=True)
        logging.info(f"Wczytano {len(df)} wierszy z pliku")
        logging.info(f"Kolumny w pliku: {list(df.columns)}")

        # Przygotuj plik wyjściowy
        output_file = input_file.rsplit('.', 1)[0] + '_geocoded.csv'

        # Dodaj kolumny dla współrzędnych jeśli nie istnieją
        if 'latitude' not in df.columns:
            df['latitude'] = None
        if 'longitude' not in df.columns:
            df['longitude'] = None

        # Przetwarzaj pierwsze 5 adresów jako test
        successful_count = 0
        failed_count = 0
        processed_count = 0

        for i in tqdm(range(min(5, len(df))), desc="Test geokodowania adresów"):
            current_pos = i + 1
            address = df.iloc[i]['adress']
            row_id = df.iloc[i]['id']
            
            logging.info(f"[{current_pos}/5] Test adresu ID {row_id}: {address}")
            
            # Parsuj adres
            parsed_address = parse_csv_address(address)
            if not parsed_address:
                logging.warning(f"Nie udało się sparsować adresu: {address}")
                failed_count += 1
                continue
            
            # Sprawdź kod pocztowy
            if not is_valid_postal_code(parsed_address['postal_code']):
                logging.warning(f"Kod pocztowy {parsed_address['postal_code']} nie należy do powiatu bocheńskiego. Pomijanie adresu: {address}")
                failed_count += 1
                continue
            
            logging.info(f"Parsowanie OK: kod={parsed_address['postal_code']}, miasto={parsed_address['city']}, ulica={parsed_address['street_and_number']}")
            successful_count += 1
            processed_count += 1

        # Zapisz wyniki testu
        df_test = df.head(5).copy()
        df_test.to_csv(output_file, sep=',', index=False, quoting=1)
        logging.info(f"Zapisano wyniki testu do pliku: {output_file}")

        # Podsumowanie
        logging.info("Podsumowanie testu:")
        logging.info(f"- Pomyślnie sparsowano: {successful_count} adresów")
        logging.info(f"- Nie udało się przetworzyć: {failed_count} adresów")

    except Exception as e:
        logging.error(f"Wystąpił błąd podczas przetwarzania pliku: {str(e)}")

if __name__ == "__main__":
    # Konfiguracja logowania
    log_filename = setup_logging()
    logging.info("Rozpoczęcie programu testowego geokodowania adresów")
    
    input_file = "powiat bocheński - adresy_geocode.csv"
    process_csv(input_file)
    
    logging.info("Zakończenie programu testowego geokodowania adresów")
