import pandas as pd
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut, GeocoderServiceError
import time
import ssl
import certifi
import logging
from datetime import datetime
import sys
import os
from tqdm import tqdm

# Bounding box dla powiatu bocheńskiego
# Format dla geopy: [(lat1, lon1), (lat2, lon2)] - południowy-zachód, północny-wschód
# <PERSON><PERSON><PERSON> obejmuj<PERSON>cy powiat bocheński w województwie małopolskim
BOCHNIA_BBOX = [(49.7, 20.2), (50.1, 20.7)]

# Ko<PERSON> pocztowe powiatu bocheńskiego
BOCHNIA_POSTAL_CODES = {
    '32-700', '32-708', '32-709', '32-711', '32-712', '32-720', '32-722',
    '32-724', '32-725', '32-731', '32-732', '32-733', '32-740', '32-741',
    '32-742', '32-744', '32-765', '32-813', '32-015'
}

# <PERSON>ache dla miast - przyspiesza wyszukiwanie powtarzających się miast
CITY_CACHE = {}

def is_valid_result(lat, lon, address_text):
    """
    Sprawdza, czy wynik geokodowania jest prawidłowy.
    Zwraca True jeśli współrzędne są w powiecie bocheńskim, adres zawiera nazwę województwa
    i kod pocztowy należy do powiatu bocheńskiego.
    """
    if pd.isna(lat) or pd.isna(lon):
        return False

    # Sprawdź bounding box
    if not is_in_bbox(lat, lon, BOCHNIA_BBOX):
        return False

    # Jeśli mamy tekst adresu, sprawdź województwo i kod pocztowy
    if isinstance(address_text, str):
        address_lower = address_text.lower()

        # Sprawdź województwo
        has_voivodeship = "małopolskie" in address_lower or "małopolska" in address_lower

        # Sprawdź kod pocztowy
        postal_code = extract_postal_code(address_text)
        has_valid_postal = is_valid_postal_code(postal_code)

        return has_voivodeship and has_valid_postal

    return True  # Jeśli nie ma tekstu adresu, przyjmij że jest OK (dla kompatybilności wstecznej)

def setup_logging():
    """
    Konfiguruje system logowania.
    """
    # Tworzymy nazwę pliku logu z datą i czasem
    log_filename = f"geocoding_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    # Konfiguracja logowania
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return log_filename

def is_in_bbox(lat, lon, bbox):
    """Sprawdza, czy współrzędne znajdują się wewnątrz zadanego obszaru (bounding box)."""
    (min_lat, min_lon), (max_lat, max_lon) = bbox
    return min_lat <= lat <= max_lat and min_lon <= lon <= max_lon

def extract_postal_code(address):
    """
    Wyodrębnia kod pocztowy z adresu.
    Oczekuje formatu: "XX-XXX Miasto,ulica numer" lub podobnego.
    """
    import re
    if not isinstance(address, str):
        return None

    # Szukaj wzorca XX-XXX na początku adresu
    match = re.match(r'^(\d{2}-\d{3})', address.strip())
    if match:
        return match.group(1)
    return None

def is_valid_postal_code(postal_code):
    """
    Sprawdza, czy kod pocztowy należy do powiatu bocheńskiego.
    """
    return postal_code in BOCHNIA_POSTAL_CODES if postal_code else False

def is_in_bochnia_powiat(location):
    """
    Sprawdza, czy lokalizacja znajduje się w powiecie bocheńskim.
    Weryfikuje współrzędne, nazwę województwa i kod pocztowy w adresie.
    """
    if not location:
        return False

    # Sprawdź bounding box
    if not is_in_bbox(location.latitude, location.longitude, BOCHNIA_BBOX):
        return False

    # Sprawdź, czy w adresie jest "małopolskie" lub "małopolska"
    address_lower = location.address.lower()
    has_voivodeship = "małopolskie" in address_lower or "małopolska" in address_lower

    # Sprawdź kod pocztowy w adresie
    postal_code = extract_postal_code(location.address)
    has_valid_postal = is_valid_postal_code(postal_code)

    return has_voivodeship and has_valid_postal

def geocode_with_retry(geolocator, address, max_retries=2, delay=1, viewbox=None, bounded=False):
    """
    Próbuje geokodować adres z możliwością ponownych prób w przypadku timeoutu.
    Opcjonalnie zawęża wyszukiwanie do podanego obszaru i rygorystycznie weryfikuje wynik.
    """
    for attempt in range(max_retries):
        try:
            # Strategia szybkiego wyszukiwania
            if viewbox and bounded:
                # Próba z ograniczeniami - krótki timeout
                location = geolocator.geocode(address, viewbox=viewbox, bounded=bounded, timeout=8)
                if location:
                    logging.info(f"Znaleziono z bounded=True: {location.address} ({location.latitude}, {location.longitude})")
                    return location

            if viewbox:
                # Próba z viewbox ale bez bounded - średni timeout
                location = geolocator.geocode(address, viewbox=viewbox, bounded=False, timeout=10)
                if location and is_in_bochnia_powiat(location):
                    logging.info(f"Znaleziono z viewbox: {location.address} ({location.latitude}, {location.longitude})")
                    return location
                elif location:
                    logging.warning(f"Znaleziono adres '{address}', ale poza powiatem bocheńskim: {location.address} ({location.latitude}, {location.longitude}). Odrzucanie.")

            # Ostatnia próba globalna - najkrótszy timeout
            location = geolocator.geocode(address, timeout=6)
            if location and viewbox:
                if is_in_bochnia_powiat(location):
                    logging.info(f"Znaleziono globalnie w powiecie bocheńskim: {location.address} ({location.latitude}, {location.longitude})")
                    return location
                else:
                    logging.warning(f"Znaleziono adres '{address}' globalnie, ale poza powiatem bocheńskim: {location.address} ({location.latitude}, {location.longitude}). Odrzucanie.")
                    return None
            elif location:
                logging.info(f"Znaleziono globalnie: {location.address} ({location.latitude}, {location.longitude})")
                return location

            return None # Nie znaleziono lokalizacji
        except (GeocoderTimedOut, GeocoderServiceError) as e:
            if attempt < max_retries - 1:
                logging.warning(f"Błąd geokodera dla adresu: '{address}'. Próba {attempt + 1}/{max_retries}. Błąd: {e}")
                time.sleep(delay)
                continue
            logging.error(f"Wszystkie próby dla adresu '{address}' nie powiodły się.")
    return None

def parse_csv_address(address):
    """
    Parsuje adres z pliku CSV w formacie: "XX-XXX Miasto,ulica numer"
    Zwraca słownik z komponentami adresu.
    """
    import re
    if not isinstance(address, str):
        return None

    # Wzorzec dla adresu: kod_pocztowy miasto,ulica_i_numer
    pattern = r'^(\d{2}-\d{3})\s+([^,]+),(.+)$'
    match = re.match(pattern, address.strip())

    if match:
        postal_code = match.group(1)
        city = match.group(2).strip()
        street_and_number = match.group(3).strip()

        return {
            'postal_code': postal_code,
            'city': city,
            'street_and_number': street_and_number,
            'full_address': address
        }

    return None

def extract_street_without_number(address):
    """
    Próbuje wyodrębnić nazwę ulicy bez numeru z adresu.
    Np. "Kraków,Floriańska 15" -> "Kraków,Floriańska"
    """
    import re
    # Usuń numery z końca (cyfry, litery, znaki specjalne)
    street_pattern = r'^(.+?)[\s,]+\d+[a-zA-Z]*[\s/\-\d]*$'
    match = re.match(street_pattern, address.strip())
    if match:
        return match.group(1).strip()
    return None

def geocode_address(geolocator, address):
    """
    Geokoduje adres używając zoptymalizowanych strategii wyszukiwania.
    Wyszukiwanie jest rygorystycznie ograniczone do powiatu bocheńskiego.
    Optymalizowane pod kątem szybkości z zachowaniem dokładności.
    Próbuje również wyszukiwanie bez numeru domu jeśli pełny adres nie zostanie znaleziony.
    Uwzględnia sprawdzanie kodów pocztowych powiatu bocheńskiego.
    """
    # Sprawdzamy, czy adres nie jest pusty
    if not isinstance(address, str) or not address.strip():
        logging.warning("Otrzymano pusty adres. Pomijanie.")
        return None, None

    # Parsuj adres z pliku CSV
    parsed_address = parse_csv_address(address)
    if not parsed_address:
        logging.warning(f"Nie udało się sparsować adresu: {address}")
        return None, None

    # Sprawdź kod pocztowy przed rozpoczęciem geokodowania
    if not is_valid_postal_code(parsed_address['postal_code']):
        logging.warning(f"Kod pocztowy {parsed_address['postal_code']} nie należy do powiatu bocheńskiego. Pomijanie adresu: {address}")
        return None, None

    logging.info(f"Parsowanie adresu: kod={parsed_address['postal_code']}, miasto={parsed_address['city']}, ulica={parsed_address['street_and_number']}")

    # Użyj oryginalnego adresu do geokodowania, ale bez kodu pocztowego na początku
    # Format: "Miasto,ulica numer"
    cleaned_address = f"{parsed_address['city']},{parsed_address['street_and_number']}"

    try:
        # Próba 1: Pełny adres bez dodatkowych słów (najczęściej najlepsze wyniki)
        logging.info(f"Próba 1: Wyszukiwanie pełnego adresu: '{cleaned_address}'")
        location = geocode_with_retry(geolocator, cleaned_address, viewbox=BOCHNIA_BBOX, bounded=False)
        if location and is_in_bochnia_powiat(location):
            logging.info(f"Znaleziono współrzędne dla pełnego adresu: {cleaned_address}")
            return location.latitude, location.longitude
        elif location:
            logging.warning(f"Znaleziono adres globalnie, ale poza powiatem bocheńskim: {location.address} ({location.latitude}, {location.longitude})")

        # Próba 2: Pełny adres z województwem (dla trudnych przypadków)
        full_address_with_voivodeship = f"{cleaned_address}, małopolskie"
        logging.info(f"Próba 2: Wyszukiwanie pełnego adresu z województwem: '{full_address_with_voivodeship}'")
        location = geocode_with_retry(geolocator, full_address_with_voivodeship, viewbox=BOCHNIA_BBOX, bounded=True)
        if location and is_in_bochnia_powiat(location):
            logging.info(f"Znaleziono współrzędne dla pełnego adresu z województwem: {cleaned_address}")
            return location.latitude, location.longitude
        elif location:
            logging.warning(f"Znaleziono adres z województwem, ale poza powiatem bocheńskim: {location.address} ({location.latitude}, {location.longitude})")

        # Próba 3: Sama ulica bez numeru (jeśli adres zawiera numer)
        street_without_number = extract_street_without_number(cleaned_address)
        if street_without_number and street_without_number != cleaned_address:
            logging.info(f"Próba 3: Wyszukiwanie ulicy bez numeru: '{street_without_number}'")
            location = geocode_with_retry(geolocator, street_without_number, viewbox=BOCHNIA_BBOX, bounded=False)
            if location and is_in_bochnia_powiat(location):
                logging.info(f"Znaleziono współrzędne dla ulicy bez numeru: {street_without_number}")
                return location.latitude, location.longitude
            elif location:
                logging.warning(f"Znaleziono ulicę bez numeru, ale poza powiatem bocheńskim: {location.address} ({location.latitude}, {location.longitude})")

        # Próba 4: Samo miasto (fallback) - z cache'owaniem
        city = parsed_address['city']
        if city in CITY_CACHE:
            logging.info(f"Znaleziono miasto w cache: {city}")
            cached_coords = CITY_CACHE[city]
            if cached_coords:  # Jeśli nie jest None
                return cached_coords
        else:
            logging.info(f"Próba 4: Wyszukiwanie miasta: '{city}'")
            location = geocode_with_retry(geolocator, city, viewbox=BOCHNIA_BBOX, bounded=False)
            if location and is_in_bochnia_powiat(location):
                logging.info(f"Znaleziono współrzędne dla miasta: {city}")
                coords = (location.latitude, location.longitude)
                CITY_CACHE[city] = coords  # Zapisz w cache
                return coords
            elif location:
                logging.warning(f"Znaleziono miasto, ale poza powiatem bocheńskim: {location.address} ({location.latitude}, {location.longitude})")
                CITY_CACHE[city] = None  # Zapisz w cache jako nieznalezione w powiecie bocheńskim
            else:
                CITY_CACHE[city] = None  # Zapisz w cache jako nieznalezione

        # Próba 5: Miasto z województwem (ostatnia próba)
        city_with_voivodeship = f"{city}, małopolskie"
        logging.info(f"Próba 5: Wyszukiwanie miasta z województwem: '{city_with_voivodeship}'")
        location = geocode_with_retry(geolocator, city_with_voivodeship, viewbox=BOCHNIA_BBOX, bounded=True)
        if location and is_in_bochnia_powiat(location):
            logging.info(f"Znaleziono współrzędne dla miasta z województwem: {city}")
            return location.latitude, location.longitude
        elif location:
            logging.warning(f"Znaleziono miasto z województwem, ale poza powiatem bocheńskim: {location.address} ({location.latitude}, {location.longitude})")

        logging.warning(f"Nie znaleziono współrzędnych w powiecie bocheńskim: {address}")
        return None, None

    except Exception as e:
        logging.error(f"Nieoczekiwany błąd podczas geokodowania adresu: {address}. Błąd: {e}", exc_info=True)
        return None, None

def load_existing_results(output_file):
    """
    Wczytuje istniejące wyniki z pliku wyjściowego jeśli istnieje.
    """
    if os.path.exists(output_file):
        try:
            existing_df = pd.read_csv(output_file, sep=';')
            logging.info(f"Znaleziono istniejący plik wyników: {output_file}")
            logging.info(f"Wczytano {len(existing_df)} już przetworzonych wierszy")
            return existing_df
        except Exception as e:
            logging.warning(f"Nie udało się wczytać istniejącego pliku: {e}")
    return None

def save_progress(df, output_file):
    """
    Zapisuje aktualny postęp do pliku.
    """
    try:
        df.to_csv(output_file, sep=';', index=False)
        logging.debug(f"Zapisano postęp do pliku: {output_file}")
    except Exception as e:
        logging.error(f"Błąd podczas zapisywania postępu: {e}")

def process_csv(input_file):
    """
    Przetwarza plik CSV z adresami i zapisuje wyniki geokodowania.
    Obsługuje wznowienie pracy po przerwaniu i zapisuje wyniki na bieżąco.
    """
    try:
        logging.info(f"Rozpoczynam przetwarzanie pliku: {input_file}")

        # Wczytaj dane
        df = pd.read_csv(input_file, sep=';')
        logging.info(f"Wczytano {len(df)} wierszy z pliku")

        # Przygotuj plik wyjściowy
        output_file = input_file.rsplit('.', 1)[0] + '_geocoded.csv'

        # Sprawdź, czy istnieją już wyniki
        existing_df = load_existing_results(output_file)
        if existing_df is not None and 'latitude' in existing_df.columns and 'longitude' in existing_df.columns:
            # Połącz z istniejącymi wynikami
            df = df.merge(existing_df[['id', 'latitude', 'longitude']], on='id', how='left', suffixes=('', '_existing'))
            # Użyj istniejących współrzędnych jeśli są dostępne
            if 'latitude_existing' in df.columns:
                df['latitude'] = df['latitude_existing'].fillna(df.get('latitude'))
                df['longitude'] = df['longitude_existing'].fillna(df.get('longitude'))
                df = df.drop(columns=['latitude_existing', 'longitude_existing'], errors='ignore')

        # Dodaj kolumny dla współrzędnych jeśli nie istnieją
        if 'latitude' not in df.columns:
            df['latitude'] = None
        if 'longitude' not in df.columns:
            df['longitude'] = None

        # Inicjalizuj geokoder
        logging.info("Inicjalizacja geokodera...")
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        geolocator = Nominatim(user_agent="my_geocoder", ssl_context=ssl_context)
        logging.info("Geokoder gotowy.")

        # Przetwarzaj każdy wiersz
        successful_count = 0
        failed_count = 0
        processed_count = 0

        for i, row in enumerate(tqdm(df.itertuples(index=True), total=len(df), desc="Geokodowanie adresów")):
            current_pos = i + 1
            address = row.adress
            logging.info(f"[{current_pos}/{len(df)}] Przetwarzanie adresu ID {row.id}: {address}")

            # Sprawdź, czy adres już został przetworzony i czy wynik jest prawidłowy
            if pd.notna(row.latitude) and pd.notna(row.longitude):
                if is_valid_result(row.latitude, row.longitude, address):
                    logging.info(f"Adres już przetworzony z prawidłowymi współrzędnymi. Pomijanie.")
                    successful_count += 1
                    continue
                else:
                    logging.info(f"Adres przetworzony, ale współrzędne poza powiatem bocheńskim lub nieprawidłowy kod pocztowy. Ponowne przetwarzanie.")

            # Geokoduj adres
            try:
                lat, lon = geocode_address(geolocator, address)
                df.at[row.Index, 'latitude'] = lat
                df.at[row.Index, 'longitude'] = lon

                if lat is not None and lon is not None:
                    successful_count += 1
                    logging.info(f"Pomyślnie przetworzono adres: {address}")
                else:
                    failed_count += 1
                    logging.warning(f"Nie udało się przetworzyć adresu: {address}")
            except Exception as e:
                failed_count += 1
                logging.error(f"Błąd podczas przetwarzania adresu '{address}': {e}")
                df.at[row.Index, 'latitude'] = None
                df.at[row.Index, 'longitude'] = None

            processed_count += 1

            # Zapisuj postęp co 10 adresów
            if processed_count % 10 == 0:
                save_progress(df, output_file)

            # Dodaj małe opóźnienie, aby nie przekroczyć limitów API
            time.sleep(0.5)

        # Zapisz końcowe wyniki
        save_progress(df, output_file)
        logging.info(f"Zapisano wyniki do pliku: {output_file}")

        # Podsumowanie
        logging.info("Podsumowanie:")
        logging.info(f"- Pomyślnie przetworzono: {successful_count} adresów")
        logging.info(f"- Nie udało się przetworzyć: {failed_count} adresów")

    except Exception as e:
        logging.error(f"Wystąpił błąd podczas przetwarzania pliku: {str(e)}")

if __name__ == "__main__":
    # Konfiguracja logowania
    log_filename = setup_logging()
    logging.info("Rozpoczęcie programu geokodowania adresów")
    
    input_file = "powiat bocheński - adresy_geocode.csv"
    process_csv(input_file)
    
    logging.info("Zakończenie programu geokodowania adresów") 