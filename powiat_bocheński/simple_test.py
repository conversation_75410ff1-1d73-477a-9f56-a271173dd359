#!/usr/bin/env python3

# Prosty test nowej logiki
from geocode_addresses import extract_postal_code, is_valid_postal_code, parse_csv_address

# Test 1: Wyodrębnianie kodu pocztowego
print("Test 1: Wyodrębnianie kodu pocztowego")
test_address = "32-722 Królówka,Królówka 263"
postal_code = extract_postal_code(test_address)
print(f"Adres: {test_address}")
print(f"Kod pocztowy: {postal_code}")

# Test 2: Sprawdzanie poprawności kodu
print(f"\nTest 2: Sprawdzanie poprawności kodu {postal_code}")
is_valid = is_valid_postal_code(postal_code)
print(f"Czy kod {postal_code} jest prawidłowy dla powiatu bocheńskiego: {is_valid}")

# Test 3: Parsowanie adresu
print(f"\nTest 3: Parsowanie adresu")
parsed = parse_csv_address(test_address)
if parsed:
    print(f"Kod pocztowy: {parsed['postal_code']}")
    print(f"Miasto: {parsed['city']}")
    print(f"Ulica i numer: {parsed['street_and_number']}")
else:
    print("Nie udało się sparsować adresu")

print("\nTest zakończony pomyślnie!")
