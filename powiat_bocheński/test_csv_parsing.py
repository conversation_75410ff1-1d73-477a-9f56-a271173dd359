#!/usr/bin/env python3

import pandas as pd

print("Test parsowania pliku CSV")

# Test wczytywania pliku
input_file = "powiat bocheński - adresy_geocode.csv"

try:
    # Wczytaj dane - u<PERSON><PERSON>wamy przecinka jako separatora i obsługujemy cudzysłowy
    df = pd.read_csv(input_file, sep=',', quotechar='"', skipinitialspace=True)
    print(f"Wczytano {len(df)} wierszy z pliku")
    print(f"Kolumny w pliku: {list(df.columns)}")
    
    # <PERSON><PERSON>ż pierwsze 3 wiersze
    print("\nPierwsze 3 wiersze:")
    for i in range(min(3, len(df))):
        print(f"Wiersz {i}:")
        print(f"  id: {df.iloc[i]['id']}")
        print(f"  adress: {df.iloc[i]['adress']}")
        print()
    
    print("Test zakończony pomyślnie!")
    
except Exception as e:
    print(f"Błąd: {e}")
    import traceback
    traceback.print_exc()
