#!/usr/bin/env python3
"""
Skrypt do analizy logów i ponownego geokodowania adresów poza powiatem dąbrowskim.
Znajduje adresy w innych powiatach i próbuje je ponownie geokodować z naciskiem na powiat dąbrowski.
"""

import pandas as pd
import re
import logging
from datetime import datetime
import os
import ssl
import certifi
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut, GeocoderServiceError
import time

# Granice geograficzne powiatu dąbrowskiego (małopolskie)
# Współrzędne przybliżone na podstawie granic administracyjnych
DABROWSKI_BOUNDS = {
    'min_lat': 49.95,   # Południowa granica
    'max_lat': 50.45,   # Północna granica
    'min_lon': 20.65,   # Zachodnia granica
    'max_lon': 21.35    # Wschodnia granica
}

def is_within_dabrowski_bounds(lat, lon):
    """
    Sprawdza czy współrzędne mieszczą się w granicach powiatu dąbrowskiego.

    Args:
        lat (float): Szerokość geograficzna
        lon (float): Długość geograficzna

    Returns:
        bool: True jeśli współrzędne są w granicach powiatu dąbrowskiego
    """
    if lat is None or lon is None:
        return False

    return (DABROWSKI_BOUNDS['min_lat'] <= lat <= DABROWSKI_BOUNDS['max_lat'] and
            DABROWSKI_BOUNDS['min_lon'] <= lon <= DABROWSKI_BOUNDS['max_lon'])

# Konfiguracja logowania
def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"fix_powiat_log_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return log_filename

def parse_log_for_wrong_powiat(log_file):
    """
    Analizuje plik logów i znajduje adresy poza powiatem dąbrowskim.
    Zwraca listę słowników z informacjami o adresach do ponownego przetworzenia.
    """
    wrong_powiat_addresses = []
    
    # Wzorce do wyszukiwania w logach
    address_pattern = r'\[(\d+)/\d+\] Przetwarzanie adresu ID (\d+): (.+)'
    success_pattern = r'Znaleziono.*powiat (\w+).*województwo małopolskie.*\(([0-9.]+), ([0-9.]+)\)'
    
    current_address = None
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            # Sprawdź, czy to linia z przetwarzaniem adresu
            address_match = re.search(address_pattern, line)
            if address_match:
                current_address = {
                    'position': int(address_match.group(1)),
                    'id': address_match.group(2),
                    'address': address_match.group(3)
                }
                continue
            
            # Sprawdź, czy to linia z sukcesem geokodowania
            success_match = re.search(success_pattern, line)
            if success_match and current_address:
                powiat = success_match.group(1)
                lat = float(success_match.group(2))
                lon = float(success_match.group(3))

                # Sprawdź czy adres jest poza powiatem dąbrowskim LUB poza granicami geograficznymi
                is_wrong_powiat = powiat != 'dąbrowski'
                is_outside_bounds = not is_within_dabrowski_bounds(lat, lon)

                if is_wrong_powiat or is_outside_bounds:
                    reason = []
                    if is_wrong_powiat:
                        reason.append(f"powiat {powiat}")
                    if is_outside_bounds:
                        reason.append(f"poza granicami geograficznymi ({lat}, {lon})")

                    wrong_powiat_addresses.append({
                        'id': current_address['id'],
                        'address': current_address['address'],
                        'current_powiat': powiat,
                        'current_lat': lat,
                        'current_lon': lon,
                        'position': current_address['position'],
                        'reason': ' + '.join(reason)
                    })
                    logging.info(f"Znaleziono problematyczny adres: ID {current_address['id']}, {current_address['address']} -> {' + '.join(reason)}")

                current_address = None
    
    return wrong_powiat_addresses

def extract_city_from_address(address):
    """
    Wyodrębnia nazwę miejscowości z adresu.
    Np. "Wielopole,Wielopole 149" -> "Wielopole"
    """
    # Usuń przecinki i weź pierwszą część
    city = address.split(',')[0].strip()
    return city

def geocode_with_dabrowski_focus(geolocator, address):
    """
    Geokoduje adres z naciskiem na powiat dąbrowski.
    Próbuje różne strategie i sprawdza granice geograficzne.
    """
    # Wyodrębnij miejscowość z adresu
    city = extract_city_from_address(address)

    strategies = [
        # Najpierw próbuj z pełnym adresem
        f"{address}, powiat dąbrowski, małopolskie",
        f"{address}, gmina Dąbrowa Tarnowska, małopolskie",
        f"{address}, gmina Szczucin, małopolskie",
        f"{address}, gmina Gręboszów, małopolskie",

        # Potem próbuj z samą miejscowością + wszystkie gminy powiatu dąbrowskiego
        f"{city}, powiat dąbrowski, małopolskie",
        f"{city}, gmina Dąbrowa Tarnowska, małopolskie",
        f"{city}, gmina Szczucin, małopolskie",
        f"{city}, gmina Gręboszów, małopolskie",
        f"{city}, gmina Olesno, małopolskie",
        f"{city}, gmina Radgoszcz, małopolskie",
        f"{city}, gmina Bolesław, małopolskie",
        f"{city}, gmina Mędrzechów, małopolskie",

        # Ostatnie próby - tylko miejscowość
        f"{city}, Dąbrowa Tarnowska, małopolskie",
        f"{city}, małopolskie",
        f"{city}"  # Sama miejscowość jako ostatnia szansa
    ]

    for i, query in enumerate(strategies, 1):
        try:
            logging.info(f"Próba {i}: '{query}'")
            location = geolocator.geocode(query, timeout=10)

            if location:
                lat, lon = location.latitude, location.longitude

                # Sprawdź granice geograficzne NAJPIERW
                if is_within_dabrowski_bounds(lat, lon):
                    logging.info(f"✅ Znaleziono w granicach powiatu dąbrowskiego: {location.address} ({lat}, {lon})")
                    return lat, lon, location.address
                else:
                    logging.warning(f"❌ Poza granicami powiatu dąbrowskiego: {location.address} ({lat}, {lon})")

                    # Jeśli poza granicami, ale zawiera "powiat dąbrowski" w nazwie,
                    # może być błędny wynik - kontynuuj szukanie
                    if "powiat dąbrowski" in location.address.lower():
                        logging.warning(f"Wynik zawiera 'powiat dąbrowski' ale jest poza granicami - szukam dalej")

            time.sleep(1)  # Opóźnienie między próbami

        except (GeocoderTimedOut, GeocoderServiceError) as e:
            logging.warning(f"Błąd geokodowania dla '{query}': {e}")
            time.sleep(2)

    logging.error(f"Nie znaleziono żadnego wyniku w granicach powiatu dąbrowskiego dla: {address}")
    return None, None, None

def update_geocoded_file(geocoded_file, updates):
    """
    Aktualizuje plik z wynikami geokodowania.
    """
    if not os.path.exists(geocoded_file):
        logging.error(f"Plik {geocoded_file} nie istnieje!")
        return False
    
    # Wczytaj istniejące dane
    df = pd.read_csv(geocoded_file, sep=';')
    logging.info(f"Wczytano {len(df)} wierszy z pliku {geocoded_file}")
    
    updated_count = 0
    
    for update in updates:
        # Znajdź wiersz do aktualizacji
        mask = df['id'] == int(update['id'])
        if mask.any():
            df.loc[mask, 'latitude'] = update['new_lat']
            df.loc[mask, 'longitude'] = update['new_lon']
            updated_count += 1
            logging.info(f"Zaktualizowano adres ID {update['id']}: {update['address']}")
        else:
            logging.warning(f"Nie znaleziono adresu ID {update['id']} w pliku")
    
    # Zapisz zaktualizowane dane
    backup_file = geocoded_file.replace('.csv', '_backup.csv')
    df.to_csv(backup_file, sep=';', index=False)
    logging.info(f"Utworzono kopię zapasową: {backup_file}")
    
    df.to_csv(geocoded_file, sep=';', index=False)
    logging.info(f"Zaktualizowano {updated_count} adresów w pliku {geocoded_file}")
    
    return True

def main():
    log_filename = setup_logging()
    logging.info("Rozpoczęcie analizy logów i poprawiania adresów poza powiatem dąbrowskim")
    logging.info(f"Granice geograficzne powiatu dąbrowskiego: {DABROWSKI_BOUNDS}")
    
    # Znajdź najnowszy plik logów
    log_files = [f for f in os.listdir('.') if f.startswith('geocoding_log_') and f.endswith('.log')]
    if not log_files:
        logging.error("Nie znaleziono plików logów!")
        return
    
    latest_log = max(log_files, key=lambda x: os.path.getmtime(x))
    logging.info(f"Analizuję plik logów: {latest_log}")
    
    # Znajdź adresy poza powiatem dąbrowskim
    wrong_addresses = parse_log_for_wrong_powiat(latest_log)
    logging.info(f"Znaleziono {len(wrong_addresses)} problematycznych adresów")

    # Statystyki problemów
    powiat_issues = sum(1 for addr in wrong_addresses if addr['current_powiat'] != 'dąbrowski')
    bounds_issues = sum(1 for addr in wrong_addresses if not is_within_dabrowski_bounds(addr['current_lat'], addr['current_lon']))
    logging.info(f"- Problemy z powiatem: {powiat_issues}")
    logging.info(f"- Problemy z granicami geograficznymi: {bounds_issues}")
    
    if not wrong_addresses:
        logging.info("Wszystkie adresy są już w powiecie dąbrowskim!")
        return
    
    # Wyświetl podsumowanie
    print(f"\nZnaleziono {len(wrong_addresses)} problematycznych adresów:")
    for addr in wrong_addresses[:10]:  # Pokaż pierwsze 10
        print(f"- ID {addr['id']}: {addr['address']} -> {addr.get('reason', f'powiat {addr['current_powiat']}')}")
    if len(wrong_addresses) > 10:
        print(f"... i {len(wrong_addresses) - 10} więcej")
    
    # Zapytaj użytkownika o kontynuację
    response = input(f"\nCzy chcesz spróbować ponownie geokodować te {len(wrong_addresses)} adresów? (t/n): ")
    if response.lower() not in ['t', 'tak', 'y', 'yes']:
        logging.info("Anulowano przez użytkownika")
        return
    
    # Inicjalizuj geokoder
    ssl_context = ssl.create_default_context(cafile=certifi.where())
    geolocator = Nominatim(user_agent="fix_powiat_geocoder", ssl_context=ssl_context)
    
    # Ponownie geokoduj adresy
    updates = []
    successful_fixes = 0
    
    for i, addr in enumerate(wrong_addresses, 1):
        logging.info(f"[{i}/{len(wrong_addresses)}] Ponowne geokodowanie ID {addr['id']}: {addr['address']}")
        
        new_lat, new_lon, new_address = geocode_with_dabrowski_focus(geolocator, addr['address'])
        
        if new_lat and new_lon:
            updates.append({
                'id': addr['id'],
                'address': addr['address'],
                'new_lat': new_lat,
                'new_lon': new_lon,
                'new_address': new_address
            })
            successful_fixes += 1
            logging.info(f"Poprawiono adres ID {addr['id']}")
        else:
            logging.warning(f"Nie udało się poprawić adresu ID {addr['id']}")
        
        time.sleep(1)  # Opóźnienie między adresami
    
    logging.info(f"Pomyślnie poprawiono {successful_fixes}/{len(wrong_addresses)} adresów")
    
    # Aktualizuj plik z wynikami
    if updates:
        geocoded_file = "adresy_geocoded.csv"
        if update_geocoded_file(geocoded_file, updates):
            logging.info("Pomyślnie zaktualizowano plik z wynikami")
        else:
            logging.error("Błąd podczas aktualizacji pliku")
    
    logging.info("Zakończenie procesu poprawiania adresów")

if __name__ == "__main__":
    main()
