#!/usr/bin/env python3

from geocode_addresses import extract_postal_code, is_valid_postal_code

# Test wyodrębniania kodu pocztowego z różnych formatów adresów
test_addresses = [
    # Format z pliku CSV (kod na początku)
    "32-731 Żegocina,Żegocina 321",
    
    # Format z geocodera (kod na końcu)
    "Żegocina, gmina Żegocina, powiat bocheński, województwo małopolskie, 32-731, Polska",
    
    # Inne formaty
    "321, Żegocina, gmina Żegocina, powiat bocheński, województwo małopolskie, 32-731, Polska",
    "Żegocina, 965, <PERSON><PERSON><PERSON>ia<PERSON>, Żegocina, gmina Żegocina, powiat bocheński, województwo małopolskie, 32-731, Polska"
]

print("Test wyodrębniania kodów pocztowych:")
print("=" * 50)

for address in test_addresses:
    postal_code = extract_postal_code(address)
    is_valid = is_valid_postal_code(postal_code)
    
    print(f"Adres: {address}")
    print(f"Kod pocztowy: {postal_code}")
    print(f"Czy prawidłowy dla powiatu bocheńskiego: {is_valid}")
    print("-" * 50)

print("\nTest zakończony!")
